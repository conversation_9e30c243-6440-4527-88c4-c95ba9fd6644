<?php

namespace app\apitp\controller;

use app\common\controller\Api;
use app\common\model\Holidays;
use think\Cache;


/**
 * 示例接口
 */
class Venue extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();
    }

    //todo,后台补充了才开发
    /**
     * 获取未来7天的场馆开放信息
     * @return Response
     */        
    public function getVenueInfo()
    {
         // 尝试从缓存读取
        // $cacheKey = 'holiday_state:holiday';   
        // $result = Cache::store('redis')->get($cacheKey);
        // if ($result) {
        //     return json(['code' => 1, 'msg' => 'success', 'data' => json_decode($result, true)]);
        // }

        // 当前日期
        $now = date('Y-m-d');
        // 第7天日期（含今天共7天）
        $end = date('Y-m-d', strtotime('+6 day'));

        // 查询 holidays 表
        $list = Holidays::name('holidays')
            ->field([
                'id',
                'holidays_date as day',
                'holidays_week as dayOfWeek',
                'is_close as isClose'
            ])
            ->where('holidays_date', '>=', $now)
            ->where('holidays_date', '<=', $end)
            ->order('holidays_date asc')
            ->select();
        // 写入 Redis 缓存，30 分钟
        // Cache::store('redis')->set($cacheKey, json_encode($list), 1800);

        return $this->success('获取成功', $list);
    }


    public function getVenueDetail()
    {
        // 获取入参
        $data = $this->request->getInput(); // 原始字符串
        $param = json_decode($data, true);
        // $param = $this->reques->post();
        if (empty($param['date'])) {
               // return $this->error('日期不能为空!');
        }

        $date = date('Y-m-d', strtotime($param['date']));
        $now = date('Y-m-d');
        $max = date('Y-m-d', strtotime('+6 days'));

        // 检查是否在合法范围内（今天~7天内）
        if ($date < $now || $date > $max) {
            return $this->error('场次过期或未开始!');
        }

        // $key = 'venue_state:' . $date;
        // $cached = Cache::store('redis')->get($key);
        // if ($cached) {
        //     return json(['code' => 1, 'msg' => 'success', 'data' => json_decode($cached, true)]);
        // }

        // 查询场次
        $list = \app\common\model\Venue::name('venue')
            ->alias('v')
            ->field([
                'v.id',
                'v.venue_poll'       => 'venuePoll',
                'v.venue_start_time' => 'venueStartTime',
                'v.venue_end_time'   => 'venueEndTime',
                'v.is_open'          => 'isOpen',
                'v.week',
                'v.inventory_votes'  => 'inventoryVotes',
            ])
            ->whereRaw("DATE_FORMAT(v.venue_start_time,'%y%m%d') = DATE_FORMAT(?, '%y%m%d')", [$date])
            ->where('v.del_flag', 0)
            ->where('v.is_open', 1)
            ->order('v.venue_start_time', 'asc')
            ->select();

        // // 写入缓存（3小时）
        // if ($list) {
        //     Cache::store('redis')->set($key, json_encode($list), 10800);
        // }

        return $this->success('获取成功', $list);
    }

    /**
     * 显示我的签到记录
     * @ApiTitle    (显示我的签到记录)
     * @ApiSummary  (获取用户的场馆预约和签到记录，支持分页)
     * @ApiMethod   (GET)
     * @ApiRoute    (/apitp/venue/showMySign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="pageNum", type="integer", required=false, description="页码，默认1")
     * @ApiParams   (name="pageSize", type="integer", required=false, description="每页数量，默认10")
     */
    public function showMySign()
    {
        
            // 获取用户ID 
            $userId = $this->getUserId();
            // 获取分页参数 
            $pageNum = $this->request->param('pageNum', 1, 'intval');
            $pageSize = $this->request->param('pageSize', 10, 'intval');
            
            // 查询总数 
            $total = $this->getVenueSignCount($userId);
            $list = [];
            if ($total > 0) {
                // 计算分页参数 
                $start = ($pageNum - 1) * $pageSize;
                $end = $pageSize;
                
                // 查询分页数据
                $list = $this->getVenueSignList($userId, $start, $end);
                // 处理过期状态
                $beginOfDay = strtotime(date('Y-m-d')); 
                foreach ($list as &$item) {
                
                    if ($item['flag'] == '1' && $beginOfDay > strtotime(date('Y-m-d', strtotime($item['venueEndTime'])))) {
                        $item['flag'] = '2';
                    }
                }
            }
            // 返回结果
            $result = [
                'total' => $total,
                'rows' => $list
            ];
            return $this->success('获取成功', $result);
            
        
    }

    /**
     * 获取用户场馆签到记录总数 - 完全对应Java版本queryPageCount
     * @param int $userId 用户ID
     * @return int
     */
    private function getVenueSignCount($userId)
    {
        
        $sql = "SELECT count(*) as count FROM (
                    SELECT   venue_id, user_id
                    FROM venue_subscribe
                    WHERE user_id = ?  
                    GROUP BY venue_id, user_id, del_flag, sign_state
                ) tem";
        $result = \think\Db::query($sql, [$userId]);
        return $result[0]['count'] ?? 0;
    }

    /**
     * 获取用户场馆签到记录列表 - 完全对应Java版本showMySign
     * @param int $userId 用户ID
     * @param int $start 偏移量
     * @param int $end 每页数量
     * @return array
     */
    private function getVenueSignList($userId, $start, $end)
    {
      
        $sql = "SELECT v.id as venueId, v.venue_start_time as venueStartTime, v.venue_end_time as venueEndTime,
                       s.sign_state as signState, v.week,
                       IF(s.del_flag = '0', '1', '0') as flag, s.venue_id, s.user_id, s.type, s.number,
                       COUNT(l.linkman_name) as peopleCount
                FROM venue_subscribe s
                LEFT JOIN venue v ON v.id = s.venue_id
                LEFT JOIN user_linkman l ON l.id = s.user_linkman_id
                WHERE s.user_id = ?
                GROUP BY s.venue_id, s.user_id, v.venue_start_time, v.venue_end_time, v.week, flag, s.sign_state
                ORDER BY flag DESC, v.venue_start_time DESC
                LIMIT ?, ?";

        $result = \think\Db::query($sql, [$userId, $start, $end]);

        // 格式化数据 
        foreach ($result as &$item) {
            // 时间格式化 
            if ($item['venueStartTime']) {
                $item['venueStartTime'] = date('Y/m/d H:i:s', strtotime($item['venueStartTime']));
            }
            if ($item['venueEndTime']) {
                $item['venueEndTime'] = date('Y/m/d H:i:s', strtotime($item['venueEndTime']));
            }
            // peopleCount字段 
            $item['peopleCount'] = (string)$item['peopleCount'];
        }

        return $result;
    }

    

  

    /**
     * 取消预约
     * @ApiTitle    (取消预约)
     * @ApiSummary  (取消场馆预约)
     * @ApiMethod   (GET)
     * @ApiRoute    (/apitp/venue/cancelSign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="venueId", type="integer", required=true, description="场馆ID")
     * @ApiParams   (name="subscribeId", type="integer", required=true, description="预约ID")
     * @ApiParams   (name="type", type="integer", required=true, description="类型")
     * @ApiParams   (name="number", type="integer", required=true, description="人数")
     * @ApiParams   (name="peopleIds", type="array", required=true, description="人员ID数组")
     */
    public function cancelSign()
    {
        // 获取用户ID -
        $userId = $this->getUserId();

        // 获取参数 
        $venueId = $this->request->param('venueId', 0, 'intval');
        $subscribeId = $this->request->param('subscribeId', 0, 'intval');
        $type = $this->request->param('type', 0, 'intval');
        $number = $this->request->param('number', 0, 'intval');
        $peopleIds = $this->request->param('peopleIds', []);
        
        // 参数验证
        if (!$venueId || !$subscribeId || !isset($type) || !$number) {
            return $this->error('参数不完整');
        }
        
        // 如果peopleIds是字符串，转换为数组
        if (is_string($peopleIds)) {
            $peopleIds = explode(',', $peopleIds);
        }
        $result = $this->doCancelSign($userId, $venueId, $subscribeId, $type, $number, $peopleIds);
        
        if ($result ) {
            return $this->success('取消成功');
        } else {
            return $this->error('取消失败');
        }
    }

   

    /**
     * 显示场馆凭证
     * @ApiTitle    (显示场馆凭证)
     * @ApiSummary  (显示用户在指定场馆的预约凭证信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/apitp/venue/showVoucher)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="venueId", type="integer", required=true, description="场馆ID")
     */
    public function showVoucher()
    {
        $userId = $this->getUserId();
        $venueId = $this->request->param('venueId', 0, 'intval');
        if (!$venueId) {
            return $this->error('场馆ID不能为空');
        }
        // 检查用户是否有该场馆的预约记录
        $subscribeList = \app\common\model\VenueSubscribe::where('venue_id', $venueId)
            ->where('user_id', $userId)
            ->where('del_flag', 0)
            ->select();
        if (!$subscribeList || count($subscribeList) == 0) {
            return $this->error('未查询到记录!');
        }
        // 查询详细的凭证信息
        $voucherData = $this->getVoucherData($venueId, $userId);
        
        return $this->success('获取成功', $voucherData);
      
    }

    /**
     * 获取凭证数据
     */
    private function getVoucherData($venueId, $userId)
    {
        // 执行联表查询获取凭证信息
        $sql = "SELECT v.week, v.venue_start_time as venueStartTime, v.venue_end_time as venueEndTime, 
                       s.sign_state as signState, s.type, s.number, s.id as subscribeId,
                       l.linkman_name as linkmanName, l.linkman_certificate as linkmanCertificate, 
                       l.linkman_age as linkmanAge, l.linkman_phone as linkmanPhone, l.id as linkId
                FROM venue v
                LEFT JOIN venue_subscribe s ON v.id = s.venue_id AND s.user_id = ? AND s.del_flag = 0
                JOIN user_linkman l ON l.id = s.user_linkman_id
                WHERE v.id = ?";
        
        $result = \think\Db::query($sql, [$userId, $venueId]);
        
        if (!$result || count($result) == 0) {
            throw new \Exception('未查询到凭证信息');
        }

        // 组装返回数据
        $firstRecord = $result[0];
        $centerVoucher = [
            'week' => (int)$firstRecord['week'],
            'venueStartTime' => date('Y/m/d H:i:s', strtotime($firstRecord['venueStartTime'])),
            'venueEndTime' => date('Y/m/d H:i:s', strtotime($firstRecord['venueEndTime'])),
            'signState' => $firstRecord['signState'],
            'subscribeId' => (int)$firstRecord['subscribeId'],
            'type' => (int)$firstRecord['type'],
            'number' => (int)$firstRecord['number'],
            'peoples' => []
        ];

        // 组装人员信息
        foreach ($result as $item) {
            $centerVoucher['peoples'][] = [
                'linkId' => (int)$item['linkId'],
                'linkmanName' => $item['linkmanName'],
                'linkmanCertificate' => $item['linkmanCertificate'],
                'linkmanPhone' => $item['linkmanPhone'],
                'linkmanAge' => (int)$item['linkmanAge']
            ];
        }

        return $centerVoucher;
    }

  
    /**
     * 获取场馆二维码
     * @ApiTitle    (获取场馆二维码)
     * @ApiSummary  (获取场馆二维码)
     * @ApiMethod   (GET)
     * @ApiRoute    (/apitp/venue/getQrCode)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="venueId", type="integer", required=true, description="场馆ID")
     * @ApiParams   (name="linkId", type="integer", required=true, description="联系人ID")
     */
    public function getQrCode()
    {
       
            // 获取用户ID 
            $userId = $this->getUserId();
           
            // 获取参数 
            $venueId = $this->request->param('venueId', 0, 'intval');
            $linkId = $this->request->param('linkId', 0, 'intval');
            
            // 参数验证
            if (!$venueId || !$linkId) {
                return $this->error('参数不完整');
            }
            
            // 调用业务逻辑 
            $qrCode = $this->getQrCodeBusiness($venueId, $userId, $linkId);
            
            // 返回结果 
            return $this->success('获取成功', $qrCode);
    }

    /**
     * 获取场馆二维码业务逻辑
     * @param int $venueId 场馆ID
     * @param int $userId 用户ID
     * @param int $linkId 联系人ID
     * @return string 二维码字符串
     * @throws \Exception
     */
    private function getQrCodeBusiness($venueId, $userId, $linkId)
    {
        // 构建Redis缓存键 
        $key = "venue_subscribe:{$venueId}:{$userId}:{$linkId}";
         
        // 尝试从Redis缓存获取 - 
        $qrCode = Cache::get($key);
        if ($qrCode) {
            return $qrCode;
        }
        
        // 查询预约记录 
        $venueSubscribeList = \app\common\model\VenueSubscribe::where('venue_id', $venueId)
            ->where('user_id', $userId)
            ->where('user_linkman_id', $linkId)
            ->where('del_flag', '0')
            ->select();
            
        if (!$venueSubscribeList || count($venueSubscribeList) == 0) {
            return $this->error('未查询到记录');
        }
        
        $venueSubscribe = $venueSubscribeList[0];
        
        // 查询联系人信息 
        $userLinkman = \app\common\model\UserLinkman::where('id', $venueSubscribe['user_linkman_id'])
            ->where('del_flag', '0')
            ->find();
            
        if (!$userLinkman || $userLinkman['del_flag'] == '2') {
            return $this->error('联系人不存在');
        }
        
        // 构建内容对象 
        $content = [
            'id' => $venueSubscribe['id'],
            'venueId' => $venueId,
            'userId' => $userId,
            'linkId' => $linkId,
            'subscribeState' => $venueSubscribe['subscribe_state'],
            'signState' => $venueSubscribe['sign_state'],
            'type' => $venueSubscribe['type'],
            'number' => $venueSubscribe['number'],
            'linkmanName' => $userLinkman['linkman_name'],
            'linkmanPhone' => $userLinkman['linkman_phone'],
            'linkmanAge' => $userLinkman['linkman_age'],
            'linkmanCertificate' => $userLinkman['linkman_certificate'],
            'linkmanCertificateType' => $userLinkman['linkman_certificate_type'],
            'showTime' => date('Y-m-d H:i:s') // 对应Java中的Coding.getCurrentTime()
        ];
        
        // 转换为JSON字符串 
        $text = json_encode($content, JSON_UNESCAPED_UNICODE);
        
        // 加密处理 
        $encryptedHex = $this->encryptDecryptHex($text);
        $qrCode = "venue:" . $encryptedHex;
        
        // 缓存2分钟 
        Cache::set($key, $qrCode, 120);
        
        return $qrCode;
    }
    
    /**
     * 加密并转换为16进制 
     * @param string $src 源字符串
     * @return string 加密后的16进制字符串
     */
    private function encryptDecryptHex($src)
    {
        try {
            // 转换为GBK编码的字节数组 
            $buf = iconv('UTF-8', 'GBK//IGNORE', $src);
            $bufBytes = [];
            for ($i = 0; $i < strlen($buf); $i++) {
                $bufBytes[] = ord($buf[$i]);
            }
            
            // 执行加密解密 
            $encryptedBytes = \app\common\library\DesEncryption::encryptDecryptBuf($bufBytes);
            
            // 转换为16进制字符串 
            return $this->byte2hex($encryptedBytes);
        } catch (\Exception $e) {
            return '';
        }
    }
    
    /**
     * 字节数组转16进制字符串 
     * @param array $bytes 字节数组
     * @return string 16进制字符串（大写）
     */
    private function byte2hex($bytes)
    {
        $hs = '';
        foreach ($bytes as $b) {
            $tmp = dechex($b & 0xFF);
            if (strlen($tmp) == 1) {
                $hs .= '0' . $tmp;
            } else {
                $hs .= $tmp;
            }
        }
        return strtoupper($hs);
    }

  

  
 
    
   
  
  
    
 
  
    /**
     * 删除预约记录
     */
    private function deleteRecord($userId, $venueId, $peopleIds)
    {
        if ($peopleIds && count($peopleIds) > 0) {
            \app\common\model\VenueSubscribe::where('user_id', $userId)
                ->where('venue_id', $venueId)
                ->whereIn('user_linkman_id', $peopleIds)
                ->update(['del_flag' => '2', 'update_time' => date('Y-m-d H:i:s')]);
        }
    }

    /**
     * 执行取消预约 - 对应Java中VenueServiceImpl.cancelSign方法
     * @param int $userId 用户ID
     * @param int $venueId 场馆ID
     * @param int $subscribeId 预约ID
     * @param int $type 类型（1表示全部取消，其他值表示部分取消）
     * @param int $number 取消数量
     * @param array $peopleIds 人员ID数组
     * @return int
     */
    private function doCancelSign($userId, $venueId, $subscribeId, $type, $number, $peopleIds)
    {
        // 验证用户是否有该场馆的预约记录且未完成签到 - 对应Java中veryIfHasSign
        $stoke = \app\common\model\VenueSubscribe::where('user_id', $userId)
            ->where('venue_id', $venueId)
            ->where('del_flag', '0')
            ->where('sign_state', '0') // 未签到
            ->count();
            
        if ($stoke <= 0) {
            return $this->error('取消失败,未预约场馆或已完成签到!');
        }
        
        // 根据type参数确定要释放的库存数量 
        if ($type == 1) {
            $stoke = $number;
        } else if ($peopleIds && count($peopleIds) > 0 && $stoke > count($peopleIds)) {
            $stoke = count($peopleIds);
        }
        
        // 开始事务 
        \think\Db::startTrans();
    
        // 获取场馆信息 
        $venue = \app\common\model\Venue::where('id', $venueId)->find();
        if (!$venue) {
            return $this->error('场馆不存在');
        }
        
        // 时间校验 
        $currentTime = time();
        if ($currentTime > strtotime($venue['venue_end_time'])) {
            return $this->error('上/下午场时间已过,无法取消!');
        }
        
        // 更新场馆库存 -
        \app\common\model\Venue::where('id', $venueId)
            ->setInc('inventory_votes', $stoke);
        
        // 删除预约记录 
        if ($type == 1) {
            // 删除整个预约记录
            \app\common\model\VenueSubscribe::where('id', $subscribeId)
                ->update(['del_flag' => '2', 'update_time' => date('Y-m-d H:i:s')]);
        } else {
            // 删除指定人员的预约记录
            $this->deleteRecord($userId, $venueId, $peopleIds);
        }
        
        // 异步更新缓存 - 对应Java中的CompletableFuture.runAsync
        $this->updateVenueCacheAsync($venue, $stoke);
        
        \think\Db::commit();
        return 1;
    
    }
    
    /**
     * 异步更新场馆缓存 - 对应Java中的Redis缓存更新逻辑
     * @param array $venue 场馆信息
     * @param int $stoke 释放的库存数量
     */
    private function updateVenueCacheAsync($venue, $stoke)
    {
    
        // 对应Java中的Redis缓存key格式
        $key = 'venue_state:' . date('Y-m-d', strtotime($venue['venue_start_time']));
        
        // 尝试从缓存获取数据
        $cacheData = \think\Cache::store('redis')->get($key);
        
        if ($cacheData) {
            $webVenueResultVos = json_decode($cacheData, true);
            
            if ($webVenueResultVos && is_array($webVenueResultVos)) {
                // 更新对应场馆的库存信息
                foreach ($webVenueResultVos as &$webVenueResultVo) {
                    if ($webVenueResultVo['id'] == $venue['id']) {
                        $webVenueResultVo['inventoryVotes'] = $webVenueResultVo['inventoryVotes'] + $stoke;
                        break;
                    }
                }
                
                // 重新写入缓存，3小时过期 - 对应Java中的3l, TimeUnit.HOURS
                \think\Cache::store('redis')->set($key, json_encode($webVenueResultVos), 10800);
            }
        }
        
    }

    /**
     * 获取管理员二维码
     * @ApiTitle    (获取管理员二维码)
     * @ApiSummary  (获取管理员二维码)
     * @ApiMethod   (GET)
     * @ApiRoute    (/auth/venue/getAdminCode)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function getAdminCode()
    {
        // 获取用户ID 
        $userId = $this->getUserId();

        // 调用业务逻辑生成管理员二维码
        $qrCode = $this->generateAdminCode($userId);

        // 返回结果 
        return $this->success('获取成功', $qrCode);
    }

    /**
     * 生成管理员二维码业务逻辑
     * @param int $userId 用户ID
     * @return string 二维码字符串
     * @throws \Exception
     */
    private function generateAdminCode($userId)
    {
        // 构建Redis缓存键 
        $key = "venue_admin:{$userId}";

        // 尝试从Redis缓存获取 
        $qrCode = Cache::get($key);
        if ($qrCode) {
            return $qrCode;
        }

        // 查询用户信息 
        $sysUser = $this->getUserInfo($userId);

        // 验证用户状态
        $this->validateAdminUser($sysUser);

        // 构建内容对象 
        $content = [
            'userid' => $userId,
            'nickname' => $sysUser['nick_name'] ?? '',
            'username' => $sysUser['user_name'] ?? '',
            'phonenumber' => $sysUser['phonenumber'] ?? '',
            'showtime' => date('Y-m-d H:i:s') 
        ];

        // 转换为JSON字符串 
        $text = json_encode($content, JSON_UNESCAPED_UNICODE);

        // 加密处理 
        $encryptedHex = $this->encryptDecryptHex($text);
        $qrCode = "admin:" . $encryptedHex;

        // 缓存2分钟 
        Cache::set($key, $qrCode, 120);

        return $qrCode;
    }

    /**
     * 获取用户信息
     * @param int $userId 用户ID
     * @return array 用户信息
     * @throws \Exception
     */
    private function getUserInfo($userId)
    {
        $sysUser = \app\common\model\WebUser::where('user_id', $userId)->find();

        if (!$sysUser) {
            $this->error('未找到管理员信息');
        }

        return $sysUser->toArray();
    }

    /**
     * 验证管理员用户
     * @param array $sysUser 用户信息
     * @throws \Exception
     */
    private function validateAdminUser($sysUser)
    {
        // 验证用户未被删除 
        if ($sysUser['del_flag'] != '0') {
            $this->error('管理员已删除!');
        }

        // 验证用户未被停用 
        if ($sysUser['status'] != '0') {
            $this->error('管理员已停用!');
        }

        // 查询用户角色 
        $userRoles = $this->getUserRoles($sysUser['user_id']);
        $userRoles = collection($userRoles)->toArray();
    

        if (!$userRoles || count($userRoles) == 0) {
             $this->error('非管理员无此功能!');
        }

        // 检查是否有admin角色 
        $isAdmin = false;
        foreach ($userRoles as $role) {
            if ($role['role_key'] == 'admin') {
                $isAdmin = true;
                break;
            }
        }

        if (!$isAdmin) {
            $this->error('非管理员无此功能!');
        }
    }

    /**
     * 获取用户角色列表
     * @param int $userId 用户ID
     * @return array 角色列表
     */
    private function getUserRoles($userId)
    {
        // 查询用户角色关联表和角色表
        return \app\common\model\UserRole::alias('sur')
            ->join(['sys_role' => 'sr'], 'sur.role_id = sr.role_id')
            ->field(['sr.role_id', 'sr.role_key', 'sr.role_name'])
            ->where('sur.user_id', $userId)
            ->where('sr.del_flag', '0')
            ->where('sr.status', '0')
            ->select();
    }

     /**
     * 场馆预约接口 - 对应Java中的/auth/venue/signVenue
     * @ApiTitle    (场馆预约)
     * @ApiSummary  (用户预约场馆)
     * @ApiMethod   (POST)
     * @ApiRoute    (/apitp/venue/signVenue)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="venueId", type="integer", required=true, description="场馆场次ID")
     * @ApiParams   (name="linkIds", type="array", required=true, description="联系人ID列表")
     */
    public function signVenue()
    {
        // 获取用户ID
        $userId = $this->getUserId();

        // 获取请求参数
        $data = $this->request->getInput();
        $param = json_decode($data, true);

        // 参数验证
        if (empty($param['venueId'])) {
            return $this->error('场馆场次id不能为空');
        }

        if (empty($param['linkIds']) || !is_array($param['linkIds'])) {
            return $this->error('联系人id列表不能为空');
        }

        $venueId = (int)$param['venueId'];
        $linkIds = array_map('intval', $param['linkIds']);

        // 验证联系人权限 
        $this->selectUserLinkmanExistVenue($userId, $linkIds);

        // 执行预约逻辑
        $result = $this->doSignVenue($venueId, $userId, $linkIds);

        if ($result > 0) {
            return $this->success('预约成功');
        } else {
            return $this->error('预约失败');
        }
    }

    /**
     * 验证用户联系人权限 - 对应Java中的selectUserLinkmanExistVenue
     * @param int $userId 用户ID
     * @param array $linkIds 联系人ID列表
     * @throws \Exception
     */
    private function selectUserLinkmanExistVenue($userId, $linkIds)
    {
        // 查询用户所有有效的联系人ID 
        $userLinkmanIds = \think\Db::name('user_linkman')
            ->where('user_id', $userId)
            ->where('del_flag', '0')
            ->column('id');

        // 检查是否所有linkIds都在用户权限范围内
        foreach ($linkIds as $linkId) {
            if (!in_array($linkId, $userLinkmanIds)) {
                $this->error('本次添加的联系人越权!');
            }
        }
    }

    /**
     * 执行场馆预约业务逻辑 - 对应Java中的VenueServiceImpl.signVenue
     * @param int $venueId 场馆ID
     * @param int $userId 用户ID
     * @param array $linkIds 联系人ID列表
     * @return int
     */
    private function doSignVenue($venueId, $userId, $linkIds)
    {
        $size = count($linkIds);

        if ($size == 0) {
            $this->error('联系人不能为空!');
        }

        // 检查是否已经报名/签到
        foreach ($linkIds as $linkId) {
            $count = \think\Db::name('venue_subscribe')
                ->where('user_id', $userId)
                ->where('venue_id', $venueId)
                ->where('user_linkman_id', $linkId)
                ->where('del_flag', '0')
                ->count();

            if ($count > 0) {
                $this->error('您已报名/签到,如需更改预约人,请在未签到的情况下取消重报!');
            }
        }

        // 查询场馆信息
        $venue = \app\common\model\Venue::where('id', $venueId)->find();
        if (!$venue) {
            $this->error('该场次不存在,无法预约!');
        }

        if ($venue['del_flag'] == '2') {
            $this->error('该场次不存在,无法预约!');
        }

        if ($venue['is_open'] != '1') {
            $this->error('该场次暂时为不可预约状态,请联系管理员开启!');
        }

        // 检查时间是否已过
        $now = time();
        if ($now > strtotime($venue['venue_end_time'])) {
            $this->error('时间已过,无法预约!');
        }

        // 开始事务
        \think\Db::startTrans();

    
        // 检查库存并更新 
        $oldVotes = $venue['inventory_votes'];
        if ($oldVotes < $size) {
            $this->error('场馆余票不足,请选择其他场次');
        }

        // 更新场馆库存 
        $newVotes = $oldVotes - $size;
        \app\common\model\Venue::where('id', $venueId)
            ->update(['inventory_votes' => $newVotes]);

        // 批量保存预约记录 
        $this->saveSubscribe($venueId, $userId, 0, $size, $linkIds);

        // 异步更新缓存 
        $this->updateVenueCacheAsyncForSign($venue, $size);

        \think\Db::commit();
        return 1;

    }

    /**
     * 批量保存预约记录 - 对应Java中的saveSubscribe方法
     * @param int $venueId 场馆ID
     * @param int $userId 用户ID
     * @param int $type 类型
     * @param int $number 数量
     * @param array $linkIds 联系人ID列表
     */
    private function saveSubscribe($venueId, $userId, $type, $number, $linkIds)
    {
        $insertData = [];
        foreach ($linkIds as $linkId) {
            $insertData[] = [
                'venue_id' => $venueId,
                'user_id' => $userId,
                'user_linkman_id' => $linkId,
                'subscribe_state' => '1',
                'type' => $type,
                'number' => $number,
                'sign_state' => '0',
                'del_flag' => '0',
                'create_time' => date('Y-m-d H:i:s')
            ];
        }

        // 批量插入 
        if (!empty($insertData)) {
            \think\Db::name('venue_subscribe')->insertAll($insertData);
        }
    }

    /**
     * 异步更新场馆缓存(预约时减少库存) - 对应Java中的CompletableFuture.runAsync
     * @param array $venue 场馆信息
     * @param int $size 预约数量
     */
    private function updateVenueCacheAsyncForSign($venue, $size)
    {
        // 构建缓存key - 对应Java中的DateUtil.format
        $key = 'venue_state:' . date('Y-m-d', strtotime($venue['venue_start_time']));

        // 尝试从缓存获取数据
        $cacheData = Cache::store('redis')->get($key);

        if ($cacheData) {
            $webVenueResultVos = json_decode($cacheData, true);

            if ($webVenueResultVos && is_array($webVenueResultVos)) {
                // 更新对应场馆的库存信息 - 减少库存
                foreach ($webVenueResultVos as &$webVenueResultVo) {
                    if ($webVenueResultVo['id'] == $venue['id']) {
                        $webVenueResultVo['inventoryVotes'] -= $size;
                        break;
                    }
                }

                // 重新写入缓存，3小时过期 - 对应Java中的3l, TimeUnit.HOURS
                Cache::store('redis')->set($key, json_encode($webVenueResultVos), 10800);

                // 记录日志 - 对应Java中的log.info
                \think\Log::info("异步执行,修改某场馆剩余量,场馆id:{$venue['id']},修改值为:{$size}");
            }
        }
    }

    private function getUserId()
    {
        return   $this->auth->getUser()->user_id;
    }

}